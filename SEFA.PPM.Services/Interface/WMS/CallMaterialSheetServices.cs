using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NPOI.OpenXmlFormats.Wordprocessing;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Model.Models;
using SEFA.MKM.Model.Models;
using SEFA.PPM.IServices;
using SEFA.PPM.IServices.Interface;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface.WMS;
using SEFA.PPM.Model.ViewModels.Interface.WMS;
using SEFA.PPM.Model.ViewModels.WMS;
using SEFA.PPM.Repository.Interface.WMS;
using SqlSugar;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;
using UnitmanageEntity = SEFA.DFM.Model.Models.UnitmanageEntity;


namespace SEFA.PPM.Services.Interface.WMS
{
    /// <summary>
    /// 叫料单服务实现
    /// </summary>
    public class CallMaterialSheetServices : BaseServices<CallMaterialSheetEntity>, ICallMaterialSheetServices
    {
        private readonly ICallMaterialSheetRepository _callMaterialSheetRepository;
        private readonly ICallMaterialDetailRepository _callMaterialDetailRepository;
        private readonly IBaseRepository<ProductionOrderEntity> _productionOrderRepository;
        private readonly IBaseRepository<PromatWarehouseMappingEntity> _promatWarehouseMappingRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IWmsServices _wmsServices;
        private readonly IUser _user;

        // 定义常量
        private const string MATERIAL_TYPE_PUBLIC = "PUBLIC";

        public CallMaterialSheetServices(
            ICallMaterialSheetRepository callMaterialSheetRepository,
            ICallMaterialDetailRepository callMaterialDetailRepository,
            IBaseRepository<ProductionOrderEntity> productionOrderRepository,
            IBaseRepository<PromatWarehouseMappingEntity> promatWarehouseMappingRepository,
            IUnitOfWork unitOfWork,
            IUser user,
            IWmsServices wmsServices)
        {
            _callMaterialSheetRepository = callMaterialSheetRepository;
            _callMaterialDetailRepository = callMaterialDetailRepository;
            _productionOrderRepository = productionOrderRepository;
            _promatWarehouseMappingRepository = promatWarehouseMappingRepository;
            _unitOfWork = unitOfWork;
            _wmsServices = wmsServices;
            BaseDal = callMaterialSheetRepository;
            _user = user;
        }

        /// <summary>
        /// 批量新增叫料单
        /// </summary>
        /// <param name="requests">叫料单请求模型列表</param>
        /// <returns>操作结果</returns>
        public async Task<MessageModel<string>> AddCallMaterialSheetBatch(List<CallMaterialSheetRequestModel> requests)
        {
            _unitOfWork.BeginTran();

            try
            {
                var sheets = new List<CallMaterialSheetEntity>();
                var allDetails = new List<CallMaterialDetailEntity>();

                // 生成叫料单号前缀
                string datePart = DateTime.Now.ToString("yyyyMMdd");
                string prefix = "JL";

                // 获取当天已有的叫料单号，确定流水码起始值
                var todayCallOrders = _callMaterialSheetRepository.Db.Queryable<CallMaterialSheetEntity>()
                    .Where(c => c.RequestSheetNo.StartsWith($"{prefix}{datePart}") && c.Deleted == 0)
                    .Select(c => c.RequestSheetNo)
                    .ToList();

                int maxSequence = 0;
                if (todayCallOrders.Any())
                {
                    // 从已有的叫料单号中提取最大的流水码
                    foreach (var orderNo in todayCallOrders)
                    {
                        if (orderNo.Length >= 11 && int.TryParse(orderNo.Substring(10, 3), out int sequence))
                        {
                            if (sequence > maxSequence)
                                maxSequence = sequence;
                        }
                    }
                }

                // 获取所有相关物料的Plant信息
                var materialPlantMap = new Dictionary<string, string>();
                var materialInfoList =
                    new List<(string materialId, string materialVersionId, string productionOrderId)>();

                // 收集所有物料信息和对应的生产工单ID
                for (int i = 0; i < requests.Count; i++)
                {
                    var request = requests[i];
                    if (request.Details != null && request.Details.Any())
                    {
                        foreach (var detail in request.Details)
                        {
                            if (!string.IsNullOrEmpty(detail.MaterialId) &&
                                !string.IsNullOrEmpty(request.ProductionOrderId))
                            {
                                materialInfoList.Add((detail.MaterialId, detail.MaterialVersionId,
                                    request.ProductionOrderId));
                            }
                        }
                    }
                }

                // 根据提供的SQL逻辑查询Plant信息
                if (materialInfoList.Any())
                {
                    // 提取所有需要的ID，避免在查询中直接使用Any方法
                    var materialIds = materialInfoList.Select(m => m.materialId).Distinct().ToList();
                    var productionOrderIds = materialInfoList.Select(m => m.productionOrderId).Distinct().ToList();
                    var materialVersionIds = materialInfoList.Select(m => m.materialVersionId)
                        .Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();

                    var plantQuery = _callMaterialDetailRepository.Db
                        .Queryable<SapSegmentMaterialStepEntity, WeekScheduleBomEntity, ProductionOrderEntity>((t1, t2,
                            t3) => new object[]
                        {
                            JoinType.Left, t1.MaterialId == t2.MaterialId && t1.MaterialVersionId == t2.MaterialVer,
                            JoinType.Left, t3.ParentId == t2.ID
                        })
                        .Where((t1, t2, t3) =>
                            productionOrderIds.Contains(t3.ID)
                            && materialIds.Contains(t1.MaterialId)
                            && (string.IsNullOrEmpty(t1.MaterialVersionId) ||
                                materialVersionIds.Contains(t1.MaterialVersionId)))
                        .Select((t1, t2, t3) => new
                            { t1.MaterialId, t1.MaterialVersionId, t1.Plant, ProductionOrderId = t3.ID });

                    var plantResults = await plantQuery.ToListAsync();

                    // 建立物料ID和Plant的映射关系
                    foreach (var result in plantResults)
                    {
                        string key = $"{result.MaterialId}_{result.MaterialVersionId}_{result.ProductionOrderId}";
                        materialPlantMap[key] = result.Plant;
                    }
                }

                // 批量创建叫料单主表
                for (int i = 0; i < requests.Count; i++)
                {
                    var request = requests[i];
                    // 流水码依次递增
                    string sequencePart = (maxSequence + i + 1).ToString("D3");
                    string callOrderNo = $"{prefix}{datePart}{sequencePart}";

                    var sheet = new CallMaterialSheetEntity
                    {
                        RequestSheetNo = callOrderNo,
                        ProductionOrder = request.ProductionOrderNo,
                        ProductionOrderId = request.ProductionOrderId,
                        RequestTime = request.CallTime,
                        LineCode = request.LineSideWarehouse,
                        PositionCode = request.CallPoint,
                        CallStatus = "3", // 叫料中
                        RequestType = request.RequestType,
                        OperationType = "1", // 默认操作类型为叫料
                        CallMaterialType = request.CallMaterialType,
                        Deleted = 0
                    };

                    sheet.CreateCustomGuid(_user.Name);
                    sheets.Add(sheet);
                }

                // 批量插入叫料单主表
                var sheetResults = await _callMaterialSheetRepository.Add(sheets);
                if (sheetResults <= 0)
                {
                    _unitOfWork.RollbackTran();
                    return new MessageModel<string>
                    {
                        success = false,
                        msg = "创建叫料单主表失败",
                        response = null
                    };
                }

                // 批量创建叫料单明细
                for (int i = 0; i < requests.Count; i++)
                {
                    var request = requests[i];
                    var sheet = sheets[i];

                    if (request.Details != null && request.Details.Any())
                    {
                        int seq = 1;
                        foreach (var detail in request.Details)
                        {
                            // 根据提供的SQL逻辑获取Plant值
                            string plant = ""; // 默认值
                            string key = $"{detail.MaterialId}_{detail.MaterialVersionId}_{request.ProductionOrderId}";
                            if (materialPlantMap.ContainsKey(key))
                            {
                                plant = materialPlantMap[key];
                            }

                            var detailEntity = new CallMaterialDetailEntity
                            {
                                CallSheetId = sheet.ID,
                                DetailsNumber = seq++,
                                MaterialId = detail.MaterialId,
                                MaterialCode = detail.MaterialCode,
                                MaterialName = detail.MaterialName,
                                MaterialVersionCode = detail.MaterialVersionCode,
                                MaterialVersionId = detail.MaterialVersionId,
                                Unit = detail.Unit,
                                RequestQty = detail.RequestQty,
                                BatchNo = detail.BatchNo,
                                PalletNo = detail.PalletNo,
                                Plant = plant,
                                Deleted = 0
                            };
                            detailEntity.Create(_user.UserName);
                            allDetails.Add(detailEntity);
                        }
                    }
                }

                // 批量插入叫料单明细
                if (allDetails.Any())
                {
                    var detailResults = await _callMaterialDetailRepository.Add(allDetails);
                    if (detailResults <= 0)
                    {
                        _unitOfWork.RollbackTran();
                        return new MessageModel<string>
                        {
                            success = false,
                            msg = "创建叫料单明细失败",
                            response = null
                        };
                    }
                }

                _unitOfWork.CommitTran();

                return new MessageModel<string>
                {
                    success = true,
                    msg = $"成功批量新增 {requests.Count} 条叫料单",
                    response = string.Join(",", sheets.Select(s => s.RequestSheetNo))
                };
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return new MessageModel<string>
                {
                    success = false,
                    msg = $"批量新增叫料单失败：{ex.Message}",
                    response = null
                };
            }
        }

        /// <summary>
        /// 新增叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>操作结果</returns>
        public async Task<MessageModel<string>> AddCallMaterialSheet(CallMaterialSheetRequestModel request)
        {
            _unitOfWork.BeginTran();
            CallMaterialSheetEntity sheet = null;
            List<CallMaterialDetailEntity> details = new List<CallMaterialDetailEntity>();
            try
            {
                // 生成叫料单号
                string callOrderNo = GenerateCallOrderNo();

                // 创建叫料单主表，状态设为"叫料中"
                sheet = new CallMaterialSheetEntity
                {
                    RequestSheetNo = callOrderNo,
                    ProductionOrderId = request.ProductionOrderId,
                    RequestTime = request.CallTime,
                    LineCode = request.LineSideWarehouse,
                    PositionCode = request.CallPoint,
                    CallStatus = "3", // 叫料中
                    RequestType = request.RequestType, // 默认叫料类型为正常叫料
                    OperationType = "1", // 默认操作类型为叫料
                    CallMaterialType = request.CallMaterialType,
                    Deleted = 0
                };

                sheet.CreateCustomGuid(_user.Name);
                var sheetResult = await _callMaterialSheetRepository.Add(sheet);

                if (sheetResult <= 0)
                {
                    _unitOfWork.RollbackTran();
                    return new MessageModel<string>
                    {
                        success = false,
                        msg = "创建叫料单主表失败",
                        response = null
                    };
                }

                // 创建叫料单明细
                if (request.Details != null && request.Details.Any())
                {
                    int seq = 1;
                    foreach (var detail in request.Details)
                    {
                        var detailEntity = new CallMaterialDetailEntity
                        {
                            CallSheetId = sheet.ID,
                            DetailsNumber = seq++,
                            MaterialId = detail.MaterialId,
                            MaterialCode = detail.MaterialCode,
                            MaterialName = detail.MaterialName,
                            MaterialVersionCode = detail.MaterialVersionCode,
                            MaterialVersionId = detail.MaterialVersionId,
                            Unit = detail.Unit,
                            RequestQty = detail.RequestQty,
                            BatchNo = detail.BatchNo,
                            PalletNo = detail.PalletNo,
                            Plant = "PLANT", // 默认工厂代码，可根据实际需求修改
                            Deleted = 0
                        };
                        detailEntity.Create(_user.UserName);
                        details.Add(detailEntity);
                    }

                    var detailResults = await _callMaterialDetailRepository.Add(details);
                    if (detailResults <= 0)
                    {
                        _unitOfWork.RollbackTran();
                        return new MessageModel<string>
                        {
                            success = false,
                            msg = "创建叫料单明细失败",
                            response = null
                        };
                    }
                }

                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return new MessageModel<string>
                {
                    success = false,
                    msg = $"叫料单新增失败：{ex.Message}",
                    response = null
                };
            }

            // 叫料单创建成功后，调用WMS叫料接口
            if (sheet != null)
            {
                try
                {
                    // 获取完整的叫料单信息（包括明细）
                    var fullSheet = await GetCallMaterialSheetById(sheet.ID);
                    if (fullSheet != null)
                    {
                        // 将ViewModel转换为Entity以符合WMS接口要求
                        var sheetEntity = new CallMaterialSheetEntity
                        {
                            ID = fullSheet.ID,
                            RequestSheetNo = fullSheet.RequestSheetNo,
                            ProductionOrderId = fullSheet.ProductionOrderId,
                            RequestTime = fullSheet.RequestTime,
                            LineCode = fullSheet.LineCode,
                            PositionCode = fullSheet.PositionCode,
                            CallStatus = fullSheet.CallStatus,
                            RequestType = fullSheet.RequestType,
                            OperationType = fullSheet.OperationType,
                            Deleted = fullSheet.Deleted,
                            RequestDetailsList = fullSheet.RequestDetailsList
                        };

                        // 调用WMS叫料接口
                        var wmsResult = await _wmsServices.CallMaterialAsync(sheetEntity);

                        // 更新叫料单状态和WMS调用结果
                        _unitOfWork.BeginTran();
                        try
                        {
                            var updateSheet = await _callMaterialSheetRepository.QueryById(sheet.ID);
                            if (updateSheet != null)
                            {
                                if (wmsResult.success)
                                {
                                    // WMS调用成功，更新状态为"已叫料"
                                    updateSheet.CallStatus = "1";
                                    updateSheet.WmsResult = "调用成功";
                                }
                                else
                                {
                                    // WMS调用失败，更新状态为"叫料失败"
                                    updateSheet.CallStatus = "4";
                                    updateSheet.WmsResult = wmsResult.msg;
                                }

                                updateSheet.Modify(updateSheet.ID, _user.UserName);
                                await _callMaterialSheetRepository.Update(updateSheet);
                                _unitOfWork.CommitTran();
                            }
                        }
                        catch
                        {
                            _unitOfWork.RollbackTran();
                            // 如果更新状态失败，记录日志但不影响主流程
                            SerilogServer.LogDebug($"更新叫料单状态失败，叫料单ID: {sheet.ID}", "CallMaterialSheetServices");
                        }

                        if (wmsResult.success)
                        {
                            return new MessageModel<string>
                            {
                                success = true,
                                msg = "叫料单新增成功",
                                response = sheet.RequestSheetNo
                            };
                        }
                        else
                        {
                            return new MessageModel<string>
                            {
                                success = false,
                                msg = $"调用WMS接口失败：{wmsResult.msg}",
                                response = sheet.RequestSheetNo
                            };
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 更新叫料单状态为"叫料失败"
                    try
                    {
                        _unitOfWork.BeginTran();
                        var updateSheet = await _callMaterialSheetRepository.QueryById(sheet.ID);
                        if (updateSheet != null)
                        {
                            updateSheet.CallStatus = "4";
                            updateSheet.WmsResult = $"调用异常：{ex.Message}";
                            updateSheet.Modify(updateSheet.ID, _user.UserName);
                            await _callMaterialSheetRepository.Update(updateSheet);
                            _unitOfWork.CommitTran();
                        }
                    }
                    catch
                    {
                        _unitOfWork.RollbackTran();
                        // 如果更新状态失败，记录日志但不影响主流程
                        SerilogServer.LogDebug($"更新叫料单状态失败，叫料单ID: {sheet.ID}", "CallMaterialSheetServices");
                    }

                    return new MessageModel<string>
                    {
                        success = false,
                        msg = $"调用WMS接口异常：{ex.Message}",
                        response = sheet?.RequestSheetNo
                    };
                }
            }

            return new MessageModel<string>
            {
                success = true,
                msg = "叫料单新增成功",
                response = sheet?.RequestSheetNo
            };
        }

        /// <summary>
        /// 重新叫料
        /// </summary>
        /// <param name="sheetId">叫料单ID</param>
        /// <returns>操作结果</returns>
        public async Task<MessageModel<string>> ReCallMaterialSheet(string sheetId)
        {
            // 检查叫料单是否存在且状态为"叫料失败"
            var sheet = await _callMaterialSheetRepository.QueryById(sheetId);
            if (sheet == null)
            {
                return new MessageModel<string>
                {
                    success = false,
                    msg = "叫料单不存在",
                    response = null
                };
            }

            if (sheet.CallStatus != "4")
            {
                return new MessageModel<string>
                {
                    success = false,
                    msg = "只有叫料失败的叫料单才能重新叫料",
                    response = null
                };
            }

            // 获取叫料单明细
            var details = await GetCallMaterialDetailsBySheetId(sheetId);
            if (details == null || details.Count == 0)
            {
                return new MessageModel<string>
                {
                    success = false,
                    msg = "叫料单明细为空",
                    response = null
                };
            }

            // 更新叫料单状态为"叫料中"
            _unitOfWork.BeginTran();
            try
            {
                sheet.CallStatus = "3"; // 叫料中
                sheet.WmsResult = null; // 清空之前的WMS调用结果
                sheet.Modify(sheet.ID, _user.UserName);
                await _callMaterialSheetRepository.Update(sheet);
                _unitOfWork.CommitTran();
            }
            catch
            {
                _unitOfWork.RollbackTran();
                return new MessageModel<string>
                {
                    success = false,
                    msg = "更新叫料单状态失败",
                    response = null
                };
            }

            try
            {
                // 构造WMS调用参数
                sheet.RequestDetailsList = details;

                // 调用WMS叫料接口
                var wmsResult = await _wmsServices.CallMaterialAsync(sheet);

                // 更新叫料单状态和WMS调用结果
                _unitOfWork.BeginTran();
                try
                {
                    var updateSheet = await _callMaterialSheetRepository.QueryById(sheetId);
                    if (updateSheet != null)
                    {
                        if (wmsResult.success)
                        {
                            // WMS调用成功，更新状态为"已叫料"
                            updateSheet.CallStatus = "1";
                            updateSheet.WmsResult = "调用成功";
                        }
                        else
                        {
                            // WMS调用失败，更新状态为"叫料失败"
                            updateSheet.CallStatus = "4";
                            updateSheet.WmsResult = wmsResult.msg;
                        }

                        updateSheet.Modify(updateSheet.ID, _user.UserName);
                        await _callMaterialSheetRepository.Update(updateSheet);
                        _unitOfWork.CommitTran();
                    }
                }
                catch
                {
                    _unitOfWork.RollbackTran();
                    // 如果更新状态失败，记录日志但不影响主流程
                    SerilogServer.LogDebug($"更新叫料单状态失败，叫料单ID: {sheetId}", "CallMaterialSheetServices");
                }

                if (wmsResult.success)
                {
                    return new MessageModel<string>
                    {
                        success = true,
                        msg = "重新叫料成功",
                        response = sheet.RequestSheetNo
                    };
                }
                else
                {
                    return new MessageModel<string>
                    {
                        success = false,
                        msg = $"调用WMS接口失败：{wmsResult.msg}",
                        response = sheet.RequestSheetNo
                    };
                }
            }
            catch (Exception ex)
            {
                // 更新叫料单状态为"叫料失败"
                try
                {
                    _unitOfWork.BeginTran();
                    var updateSheet = await _callMaterialSheetRepository.QueryById(sheetId);
                    if (updateSheet != null)
                    {
                        updateSheet.CallStatus = "4";
                        updateSheet.WmsResult = $"调用异常：{ex.Message}";
                        updateSheet.Modify(updateSheet.ID, _user.UserName);
                        await _callMaterialSheetRepository.Update(updateSheet);
                        _unitOfWork.CommitTran();
                    }
                }
                catch
                {
                    _unitOfWork.RollbackTran();
                    // 如果更新状态失败，记录日志但不影响主流程
                    SerilogServer.LogDebug($"更新叫料单状态失败，叫料单ID: {sheetId}", "CallMaterialSheetServices");
                }

                return new MessageModel<string>
                {
                    success = false,
                    msg = $"调用WMS接口异常：{ex.Message}",
                    response = sheet.RequestSheetNo
                };
            }
        }

        /// <summary>
        /// 修改叫料单
        /// </summary>
        /// <param name="request">叫料单请求模型</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateCallMaterialSheet(CallMaterialSheetRequestModel request)
        {
            if (string.IsNullOrEmpty(request.ID))
                return false;

            _unitOfWork.BeginTran();
            try
            {
                // 更新叫料单主表
                var sheet = await _callMaterialSheetRepository.QueryById(request.ID);
                if (sheet == null)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                sheet.ProductionOrderId = request.ProductionOrderId;
                sheet.RequestTime = request.CallTime;
                sheet.LineCode = request.LineSideWarehouse;
                sheet.PositionCode = request.CallPoint;
                sheet.CallStatus = request.CallStatus;
                sheet.Modify(request.ID, _user.UserName);

                var sheetResult = await _callMaterialSheetRepository.Update(sheet);
                if (!sheetResult)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                // 删除原有明细
                var deleteExpression = Expressionable.Create<CallMaterialDetailEntity>()
                    .And(d => d.CallSheetId == request.ID)
                    .ToExpression();
                await _callMaterialDetailRepository.Delete(deleteExpression);

                // 创建新的明细
                if (request.Details != null && request.Details.Any())
                {
                    var details = new List<CallMaterialDetailEntity>();
                    int seq = 1;
                    foreach (var detail in request.Details)
                    {
                        var detailEntity = new CallMaterialDetailEntity
                        {
                            CallSheetId = sheet.ID,
                            DetailsNumber = seq++,
                            MaterialId = detail.MaterialId,
                            MaterialCode = detail.MaterialCode,
                            MaterialName = detail.MaterialName,
                            MaterialVersionCode = detail.MaterialVersionCode,
                            MaterialVersionId = detail.MaterialVersionId,
                            Unit = detail.Unit,
                            RequestQty = detail.RequestQty,
                            BatchNo = detail.BatchNo,
                            PalletNo = detail.PalletNo,
                            Plant = "PLANT", // 默认工厂代码，可根据实际需求修改
                            Deleted = 0
                        };
                        detailEntity.Create(sheet.CreateUserId);
                        details.Add(detailEntity);
                    }

                    var detailResults = await _callMaterialDetailRepository.Add(details);
                    if (detailResults <= 0)
                    {
                        _unitOfWork.RollbackTran();
                        return false;
                    }
                }

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 删除叫料单（逻辑删除）
        /// </summary>
        /// <param name="ids">叫料单ID列表</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteCallMaterialSheet(string[] ids)
        {
            _unitOfWork.BeginTran();
            try
            {
                // 逻辑删除叫料单主表
                var sheetExpression = Expressionable.Create<CallMaterialSheetEntity>()
                    .And(s => ids.Contains(s.ID))
                    .ToExpression();
                var sheets = await _callMaterialSheetRepository.FindList(sheetExpression);
                foreach (var sheet in sheets)
                {
                    sheet.Deleted = 1;
                    sheet.Modify(sheet.ID, _user.UserName);
                }

                await _callMaterialSheetRepository.Update(sheets);

                // 逻辑删除叫料单明细表
                var detailExpression = Expressionable.Create<CallMaterialDetailEntity>()
                    .And(d => ids.Contains(d.CallSheetId))
                    .ToExpression();
                var details = await _callMaterialDetailRepository.FindList(detailExpression);
                foreach (var detail in details)
                {
                    detail.Deleted = 1;
                    detail.Modify(detail.ID, _user.UserName);
                }

                await _callMaterialDetailRepository.Update(details);

                _unitOfWork.CommitTran();
                return true;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 分页查询叫料单
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageModel<CallMaterialHeaderView>> GetPageList(CallMaterialHeaderRequestModel request)
        {
            PageModel<CallMaterialHeaderView> result = new PageModel<CallMaterialHeaderView>();

            var query = _callMaterialSheetRepository.Db.Queryable<CallMaterialSheetEntity>();

            // 添加过滤条件
            query = query.WhereIF(!string.IsNullOrEmpty(request.WorkOrder),
                    s => s.ProductionOrderId.Contains(request.WorkOrder))
                .WhereIF(!string.IsNullOrEmpty(request.CallOrderNo),
                    s => s.RequestSheetNo.Contains(request.CallOrderNo))
                .WhereIF(!string.IsNullOrEmpty(request.CallerId), s => s.CreateUserId.Contains(request.CallerId))
                .WhereIF(!string.IsNullOrEmpty(request.CallPoint), s => s.PositionCode.Contains(request.CallPoint))
                .WhereIF(!string.IsNullOrEmpty(request.LineSideWarehouse),
                    s => s.LineCode.Contains(request.LineSideWarehouse))
                .WhereIF(request.StartTime != null && request.EndTime != null,
                    s => s.RequestTime.Date >= request.StartTime && s.RequestTime.Date <= request.EndTime)
                .WhereIF(request.CallMaterialStatus == "0", s => s.CallStatus == "1") // 根据实际状态值调整
                .Where(s => s.Deleted == 0); // 逻辑删除过滤

            RefAsync<int> dataCount = 0;
            var data = await query.OrderBy(s => s.CreateDate, OrderByType.Desc)
                .Select(s => new CallMaterialHeaderView
            {
                ID = s.ID,
                ProductionOrder = s.ProductionOrder,
                RequestSheetNo = s.RequestSheetNo,
                ProductionOrderId = s.ProductionOrderId,
                RequestType = s.RequestType,
                RequestTime = s.RequestTime,
                LineCode = s.LineCode,
                PositionCode = s.PositionCode,
                CallStatus = s.CallStatus,
                OperationType = s.OperationType,
                Deleted = s.Deleted,
                CreateUserId = s.CreateUserId,
                CreateDate = s.CreateDate
            }).ToPageListAsync(request.pageIndex, request.pageSize, dataCount);

            // 获取叫料单的明细信息
            if (data != null && data.Count > 0)
            {
                var sheetIds = data.Select(d => d.ID).ToList();
                var detailQuery = _callMaterialDetailRepository.Db.Queryable<CallMaterialDetailEntity>()
                    .Where(d => sheetIds.Contains(d.CallSheetId) && d.Deleted == 0);

                var details = await detailQuery.ToListAsync();

                // 将明细信息分组并关联到对应的叫料单
                var detailsGroup = details.GroupBy(d => d.CallSheetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var item in data)
                {
                    if (detailsGroup.ContainsKey(item.ID))
                    {
                        item.Details = detailsGroup[item.ID];
                    }
                    else
                    {
                        item.Details = new List<CallMaterialDetailEntity>();
                    }
                }
            }

            result.data = data;
            result.dataCount = dataCount;
            return result;
        }


        /// <summary>
        /// 获取叫料单详情
        /// </summary>
        /// <param name="id">叫料单ID</param>
        /// <returns>叫料单详情</returns>
        public async Task<CallMaterialSheetViewModel> GetCallMaterialSheetById(string id)
        {
            var sheet = await _callMaterialSheetRepository.QueryById(id);
            if (sheet == null || sheet.Deleted == 1)
                return null;

            var details = await _callMaterialDetailRepository.FindList(d => d.CallSheetId == id && d.Deleted == 0);

            return new CallMaterialSheetViewModel
            {
                ID = sheet.ID,
                ProductionOrder = sheet.ProductionOrder,
                RequestSheetNo = sheet.RequestSheetNo,
                ProductionOrderId = sheet.ProductionOrderId,
                RequestType = sheet.RequestType,
                RequestTime = sheet.RequestTime,
                LineCode = sheet.LineCode,
                PositionCode = sheet.PositionCode,
                CallStatus = sheet.CallStatus,
                OperationType = sheet.OperationType,
                Deleted = sheet.Deleted,
                RequestDetailsList = details
            };
        }

        /// <summary>
        /// 根据叫料单ID获取明细列表
        /// </summary>
        /// <param name="sheetId">叫料单ID</param>
        /// <returns>明细列表</returns>
        public async Task<List<CallMaterialDetailEntity>> GetCallMaterialDetailsBySheetId(string sheetId)
        {
            return await _callMaterialDetailRepository.FindList(d => d.CallSheetId == sheetId && d.Deleted == 0);
        }

        /// <summary>
        /// 根据产线Code获取工单列表
        /// </summary>
        /// <param name="lineCode"></param>
        /// <returns></returns>
        public async Task<List<ProductionOrderEntity>> GetProductionOrderListByLine(string lineCode)
        {
            var query = _productionOrderRepository.Db.Queryable<ProductionOrderEntity>();
            query = query.Where(s => s.LineCode == lineCode && s.Deleted == 0 && s.PoStatus == "2");
            var data = await query.ToListAsync();
            return data;
        }

        /// <summary>
        /// 根据工单ID获取叫料单预览列表（不插入数据库）
        /// </summary>
        /// <param name="productionOrderId">工单ID</param>
        /// <returns>叫料单预览列表</returns>
        public async Task<MessageModel<List<CallMaterialSheetPreviewModel>>> GetCallMaterialSheetsPreviewByOrder(
            string productionOrderId)
        {
            try
            {
                // 1. 获取工单物料需求数据
                var materialRequirements = await _callMaterialDetailRepository.Db
                    .Queryable<BatchConsumeRequirementEntity, PoConsumeRequirementEntity, ProductionOrderEntity,
                        MaterialEntity, MaterialVersionEntity, UnitmanageEntity>((t1, t2, t3, t4, t5, t6) =>
                        new object[]
                        {
                            JoinType.Left, t1.PoConsumeRequirementId == t2.ID,
                            JoinType.Left, t2.ProductionOrderId == t3.ID,
                            JoinType.Left, t2.MaterialId == t4.ID,
                            JoinType.Left, t2.MaterialVersionId == t5.ID,
                            JoinType.Left, t4.Unit == t6.ID
                        })
                    .Where((t1, t2, t3, t4, t5, t6) =>
                        t4.Type != MATERIAL_TYPE_PUBLIC && t4.Deleted == 0 && t2.Deleted == 0 && t3.Deleted == 0 &&
                        t3.ID == productionOrderId)
                    .Select((t1, t2, t3, t4, t5, t6) => new
                    {
                        LineCode = t3.LineCode,
                        MaterialId = t4.ID,
                        MaterialCode = t4.Code,
                        MaterialName = t4.NAME,
                        MaterialVersionId = t5.ID,
                        MaterialVersionCode = t5.MaterialVersionNumber,
                        Unit = t6.Name,
                        RequiredQty = t1.WeighingQty,
                        BatchNo = t2.MaterialLotNo
                    }).ToListAsync();

                if (materialRequirements == null || materialRequirements.Count == 0)
                {
                    return new MessageModel<List<CallMaterialSheetPreviewModel>>
                    {
                        success = false,
                        msg = "未找到工单物料需求信息",
                        response = null
                    };
                }

                // 2. 获取车间-产线-物料-仓库映射关系（按照原有逻辑：产线编码和物料编码必须都满足）
                var lineCodeList = materialRequirements.Select(q => q.LineCode).Distinct().ToList();
                var materialIdList = materialRequirements.Select(q => q.MaterialId).Distinct().ToList();

                // 先获取所有相关的映射记录
                var allRelevantMappings = await _promatWarehouseMappingRepository.FindList(a =>
                    lineCodeList.Contains(a.LineCode) && materialIdList.Contains(a.MaterialId));

                // 在内存中进行精确匹配过滤（产线编码和物料编码的组合必须都存在）
                var requirementPairs = materialRequirements.Select(q => new { q.LineCode, q.MaterialId }).ToHashSet();
                var warehouseMappings = allRelevantMappings.Where(a =>
                    requirementPairs.Contains(new { a.LineCode, a.MaterialId })).ToList();

                if (warehouseMappings == null || warehouseMappings.Count == 0)
                {
                    return new MessageModel<List<CallMaterialSheetPreviewModel>>
                    {
                        success = false,
                        msg = "未找到匹配的车间产线物料仓库映射关系",
                        response = null
                    };
                }

                // 3. 获取库存信息
                var warehouseIdList = warehouseMappings.Select(w => w.WarehouseId).Distinct().ToList();
                var materialVersionIdList = materialRequirements.Select(q => q.MaterialVersionId).Distinct().ToList();

                var inventoryList = await _callMaterialDetailRepository.Db
                    .Queryable<StorageInventoryEntity>()
                    .Where(inv => warehouseIdList.Contains(inv.StorageId) &&
                                  materialIdList.Contains(inv.MaterialId) &&
                                  materialVersionIdList.Contains(inv.MaterialVersionId) &&
                                  inv.Deleted == 0)
                    .ToListAsync();

                // 4. 按仓库分组计算叫料需求
                var callMaterialSheetsPreview = new List<CallMaterialSheetPreviewModel>();
                var warehouseGroups = warehouseMappings.GroupBy(w => w.WarehouseId);

                foreach (var warehouseGroup in warehouseGroups)
                {
                    var warehouseId = warehouseGroup.Key;
                    var warehouseMaterials = warehouseGroup.ToList();
                    var callMaterialDetails = new List<CallMaterialDetailPreviewModel>();

                    foreach (var mapping in warehouseMaterials)
                    {
                        // 查找对应的物料需求（必须产线编码和物料编码都匹配）
                        var requirement = materialRequirements.FirstOrDefault(r =>
                            r.LineCode == mapping.LineCode && r.MaterialId == mapping.MaterialId);

                        if (requirement == null) continue;

                        // 查找对应的库存
                        var inventory = inventoryList.FirstOrDefault(inv =>
                            inv.StorageId == warehouseId &&
                            inv.MaterialId == mapping.MaterialId &&
                            inv.MaterialVersionId == requirement.MaterialVersionId);

                        // 获取库存数量，如果为空则默认为0
                        var inventoryQty = inventory?.Quantity ?? 0m;
                        // 获取需求数量，如果为空则默认为0
                        var requiredQty = requirement.RequiredQty ?? 0m;
                        // 计算需要叫料的数量，确保不为负数
                        var callQty = Math.Max(0m, requiredQty - inventoryQty);

                        // 只要有需求就显示，让前端决定是否叫料
                        if (requirement.RequiredQty > 0)
                        {
                            callMaterialDetails.Add(new CallMaterialDetailPreviewModel
                            {
                                MaterialId = requirement.MaterialId,
                                MaterialCode = requirement.MaterialCode,
                                MaterialName = requirement.MaterialName,
                                MaterialVersionId = requirement.MaterialVersionId,
                                MaterialVersionCode = requirement.MaterialVersionCode,
                                Unit = requirement.Unit,
                                RequiredQty = requirement.RequiredQty,
                                InventoryQty = inventoryQty,
                                RequestQty = callQty, // 使用计算后的叫料数量
                                BatchNo = requirement.BatchNo
                            });
                        }
                    }

                    // 如果该仓库有物料需求，创建叫料单预览
                    if (callMaterialDetails.Any())
                    {
                        // 获取仓库名称（如果映射表中没有仓库名称字段，则使用WarehouseId）
                        var warehouseName = warehouseId; // 可根据实际的PromatWarehouseMappingEntity字段调整

                        var callMaterialSheetPreview = new CallMaterialSheetPreviewModel
                        {
                            RequestSheetNo =
                                $"CM{DateTime.Now:yyyyMMddHHmmss}{warehouseId.Substring(Math.Max(0, warehouseId.Length - 4))}",
                            ProductionOrderId = productionOrderId,
                            LineCode = warehouseMaterials.First().LineCode, // 使用第一个映射关系的产线代码
                            WarehouseId = warehouseId,
                            WarehouseName = warehouseName,
                            Details = callMaterialDetails
                        };

                        callMaterialSheetsPreview.Add(callMaterialSheetPreview);
                    }
                }

                return new MessageModel<List<CallMaterialSheetPreviewModel>>
                {
                    success = true,
                    msg = $"成功获取{callMaterialSheetsPreview.Count}个仓库的叫料单预览",
                    response = callMaterialSheetsPreview
                };
            }
            catch (Exception ex)
            {
                return new MessageModel<List<CallMaterialSheetPreviewModel>>
                {
                    success = false,
                    msg = $"获取叫料单预览失败：{ex.Message}",
                    response = null
                };
            }
        }

        /// <summary>
        /// 根据工单ID获取叫料单明细数据（保留原有方法）
        /// </summary>
        /// <param name="productionOrderId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<CallMaterialDetailEntity>> GetCallMaterialDetailsByOrder(string productionOrderId)
        {
            // SqlSugar查询语句替换原有的SQL查询
            var query = await _callMaterialDetailRepository.Db
                .Queryable<BatchConsumeRequirementEntity, PoConsumeRequirementEntity, ProductionOrderEntity,
                    MaterialEntity, MaterialVersionEntity, UnitmanageEntity>((t1, t2, t3, t4, t5, t6) => new object[]
                {
                    JoinType.Left, t1.PoConsumeRequirementId == t2.ID,
                    JoinType.Left, t2.ProductionOrderId == t3.ID,
                    JoinType.Left, t2.MaterialId == t4.ID,
                    JoinType.Left, t2.MaterialVersionId == t5.ID,
                    JoinType.Left, t4.Unit == t6.ID
                })
                .Where((t1, t2, t3, t4, t5, t6) =>
                    t4.Type != MATERIAL_TYPE_PUBLIC && t4.Deleted == 0 && t2.Deleted == 0 && t3.Deleted == 0 &&
                    t3.ID == productionOrderId)
                .Select((t1, t2, t3, t4, t5, t6) => new
                    CallMaterialDetailEntity
                    {
                        LineCode = t3.LineCode,
                        MaterialId = t4.ID,
                        MaterialCode = t4.Code,
                        MaterialName = t4.NAME,
                        MaterialVersionId = t5.ID,
                        MaterialVersionCode = t5.MaterialVersionNumber,
                        Unit = t6.Name,
                        RequestQty = t1.WeighingQty,
                        BatchNo = t2.MaterialLotNo
                    }).ToListAsync();

            if (query == null || query.Count == 0)
            {
                return null;
            }

            // 提取查询条件：所有唯一的LineCode和MaterialId组合
            // 如果数据量不大，可以先获取所有相关的映射记录，然后在内存中过滤
            var lineCodeList = query.Select(q => q.LineCode).Distinct().ToList();
            var materialIdList = query.Select(q => q.MaterialId).Distinct().ToList();

            var allRelevantMappings = await _promatWarehouseMappingRepository.FindList(a =>
                lineCodeList.Contains(a.LineCode) && materialIdList.Contains(a.MaterialId));

            // 在内存中进行最终过滤（这样只需要一次数据库查询）
            var queryPairs = query.Select(q => new { q.LineCode, q.MaterialId }).ToHashSet();
            var proMaStoList = allRelevantMappings.Where(a =>
                queryPairs.Contains(new { a.LineCode, a.MaterialId })).ToList();


            if (proMaStoList == null || proMaStoList.Count == 0)
            {
                return query;
            }

            // 根据proMaStoList找到对应仓库里的库存信息（GetInventoryList）
            var materialIds = proMaStoList.Select(p => p.MaterialId).ToList();
            var equipmentIds = proMaStoList.Select(p => p.WarehouseId).ToList();

            // 获取库存列表
            var inventoryList = await GetInventoryList(materialIds, null, equipmentIds);

            // 更新query中的RequestQty字段，等于requestQty - 库存数量
            foreach (var item in query)
            {
                decimal inventoryQty = 0;

                // 如果物料版本不为空，则按照物料版本计算需求
                if (!string.IsNullOrEmpty(item.MaterialVersionId))
                {
                    // 查找该物料版本在库存中的总量
                    inventoryQty = inventoryList
                        .Where(i => i.MaterialVersionId == item.MaterialVersionId &&
                                    equipmentIds.Contains(i.EquipmentId))
                        .Sum(i => i.Quantity);
                }
                else
                {
                    // 如果物料版本为空，则按照物料ID计算需求
                    inventoryQty = inventoryList
                        .Where(i => i.MaterialId == item.MaterialId && equipmentIds.Contains(i.EquipmentId))
                        .Sum(i => i.Quantity);
                }

                // 更新需求数量：原需求数量 - 库存数量，如果结果小于0，则设为0
                item.RequestQty = item.RequestQty - inventoryQty > 0 ? item.RequestQty - inventoryQty : 0;
            }

            return query;
        }


        /// <summary>
        /// 生成叫料单号 JL+日期+3位流水码
        /// </summary>
        /// <returns>叫料单号</returns>
        private string GenerateCallOrderNo()
        {
            string prefix = "JL";
            string datePart = DateTime.Now.ToString("yyyyMMdd");

            // 查询当天最大的叫料单号，获取流水码部分
            var todayCallOrders = _callMaterialSheetRepository.Db.Queryable<CallMaterialSheetEntity>()
                .Where(c => c.RequestSheetNo.StartsWith($"{prefix}{datePart}") && c.Deleted == 0)
                .Select(c => c.RequestSheetNo)
                .ToList();

            int maxSequence = 0;
            if (todayCallOrders.Any())
            {
                // 从已有的叫料单号中提取最大的流水码
                foreach (var orderNo in todayCallOrders)
                {
                    if (orderNo.Length >= 11 && int.TryParse(orderNo.Substring(10, 3), out int sequence))
                    {
                        if (sequence > maxSequence)
                            maxSequence = sequence;
                    }
                }
            }

            // 流水码加1
            string sequencePart = (maxSequence + 1).ToString("D3");
            return $"{prefix}{datePart}{sequencePart}";
        }

        /// <summary>
        /// 获取库存列表，支持根据物料ID、物料版本ID和设备ID（仓库ID）进行查询
        /// </summary>
        /// <param name="materialIdList">物料ID</param>
        /// <param name="materialVersionIdList">物料版本ID</param>
        /// <param name="equipmentIdList">设备ID（仓库ID）</param>
        /// <returns>库存列表</returns>
        public async Task<List<InventoryView>> GetInventoryList(List<string> materialIdList,
            List<string> materialVersionIdList, List<string> equipmentIdList)
        {
            var query = _callMaterialDetailRepository.Db
                .Queryable<MaterialInventoryEntity, MaterialLotEntity, MaterialSubLotEntity>((inventory, lot, subLot) =>
                    new object[]
                    {
                        JoinType.Left, inventory.LotId == lot.ID,
                        JoinType.Left, inventory.SublotId == subLot.ID
                    })
                .Where((inventory, lot, subLot) => string.IsNullOrWhiteSpace(inventory.BatchConsumeRequirementId))
                .WhereIF(materialIdList is { Count: > 0 },
                    (inventory, lot, subLot) => materialIdList.Contains(lot.MaterialId))
                .WhereIF(materialVersionIdList is { Count: > 0 },
                    (inventory, lot, subLot) => materialVersionIdList.Contains(lot.MaterialVersionId))
                .WhereIF(equipmentIdList is { Count: > 0 },
                    (inventory, lot, subLot) => equipmentIdList.Contains(inventory.EquipmentId))
                .GroupBy((inventory, lot, subLot) => new
                    { lot.MaterialId, lot.MaterialVersionId, inventory.EquipmentId, inventory.QuantityUomId })
                .Select((inventory, lot, subLot) => new InventoryView
                {
                    MaterialId = lot.MaterialId,
                    MaterialVersionId = lot.MaterialVersionId,
                    EquipmentId = inventory.EquipmentId,
                    QuantityUomId = inventory.QuantityUomId,
                    Quantity = SqlFunc.AggregateSum(inventory.Quantity)
                });
            return await query.ToListAsync();
        }
    }
}